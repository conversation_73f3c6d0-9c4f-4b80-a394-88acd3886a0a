// Zustand状态管理文件
// 此文件用于管理应用的全局状态

import { create } from 'zustand';

interface ButtonState {
  modeActive: boolean;
  businessActive: boolean;
  setModeActive: () => void;
  setBusinessActive: () => void;
}

export const useStore = create<ButtonState>((set) => ({
  modeActive: true, // 默认激活模式按键
  businessActive: false,
  // 点击模式按键时，将其状态设置为true，并将业务按键状态设置为false
  setModeActive: () => set({ modeActive: true, businessActive: false }),
  // 点击业务按键时，将其状态设置为true，并将模式按键状态设置为false
  setBusinessActive: () => set({ businessActive: true, modeActive: false }),
}));
